const bcrypt = require('bcryptjs');

// 使用与系统相同的配置
const SALT_ROUNDS = 12;
const password = 'Lostpethk2025@';

async function generatePasswordHash() {
  try {
    console.log('正在生成密码 hash...');
    console.log('密码:', password);
    console.log('Salt rounds:', SALT_ROUNDS);
    
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    console.log('\n生成的密码 hash:');
    console.log(hashedPassword);
    
    // 验证生成的 hash 是否正确
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('\n验证结果:', isValid ? '✅ 正确' : '❌ 错误');
    
    console.log('\n你可以将以下 hash 字符串直接更新到数据库的 admins 表中:');
    console.log(`UPDATE admins SET password_hash = '${hashedPassword}' WHERE username = 'admin';`);
    
  } catch (error) {
    console.error('生成密码 hash 失败:', error);
  }
}

generatePasswordHash();
